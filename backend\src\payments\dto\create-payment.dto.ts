import {
  IsString,
  IsNotEmpty,
  IsNumber,
  Min,
  IsEnum,
  IsObject,
  ValidateNested,
  IsOptional,
  ValidateIf,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

// DTO cho thông tin VNPay
export class VNPayDetailsDto {
  @ApiProperty({ description: 'URL callback sau khi thanh toán' })
  @IsString()
  @IsOptional()
  returnUrl?: string;
}

// DTO chung cho việc tạo thanh toán
export class CreatePaymentDto {
  @ApiProperty({ description: 'ID của đơn hàng cần thanh toán' })
  @IsString()
  @IsNotEmpty()
  orderId: string;

  @ApiProperty({
    description: 'Phương thức thanh toán',
    enum: ['COD', 'VNPAY'],
  })
  @IsEnum(['COD', 'VNPAY'])
  @IsNotEmpty()
  paymentMethod: string;

  @ApiProperty({ description: '<PERSON>ố tiền thanh toán' })
  @IsNumber()
  @Min(1000) // Số tiền tối thiểu là 1000 VND
  amount: number;

  @ApiProperty({ description: 'Chi tiết thanh toán cho VNPAY' })
  @ValidateIf((o) => o.paymentMethod === 'VNPAY')
  @ValidateNested()
  @Type(() => VNPayDetailsDto)
  @IsObject()
  vnpayDetails?: VNPayDetailsDto;
}
