version: "3.8"

services:
  # MongoDB Database
  mongo:
    image: mongo:6
    container_name: bookstore-mongo
    ports:
      - "27017:27017"
    volumes:
      - mongo-data:/data/db
    environment:
      MONGO_INITDB_ROOT_USERNAME: root
      MONGO_INITDB_ROOT_PASSWORD: rootpassword
      MONGO_INITDB_DATABASE: bookstore
    networks:
      - bookstore-network

  # MongoDB Admin UI
  mongo-express:
    image: mongo-express
    container_name: bookstore-mongo-express
    restart: always
    ports:
      - "8081:8081"
    environment:
      ME_CONFIG_MONGODB_ADMINUSERNAME: root
      ME_CONFIG_MONGODB_ADMINPASSWORD: rootpassword
      ME_CONFIG_MONGODB_URL: ***************************************/
    depends_on:
      - mongo
    networks:
      - bookstore-network

  # Redis for caching (to be used later)
  redis:
    image: redis:7-alpine
    container_name: bookstore-redis
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - bookstore-network

  # Backend service (NestJS)
  # Uncomment when backend is ready to be containerized
  # backend:
  #   build:
  #     context: ./backend
  #     dockerfile: Dockerfile
  #   container_name: bookstore-backend
  #   ports:
  #     - "3000:3000"
  #   volumes:
  #     - ./backend:/app
  #     - /app/node_modules
  #   depends_on:
  #     - mongo
  #     - redis
  #   environment:
  #     - NODE_ENV=development
  #     - MONGO_URI=******************************************************************
  #     - REDIS_URI=redis://redis:6379
  #     - JWT_SECRET=your_jwt_secret
  #     - PORT=3000
  #   networks:
  #     - bookstore-network

  # Frontend service (React)
  # Uncomment when frontend is ready to be containerized
  # frontend:
  #   build:
  #     context: ./frontend
  #     dockerfile: Dockerfile
  #   container_name: bookstore-frontend
  #   ports:
  #     - "3001:3000"
  #   volumes:
  #     - ./frontend:/app
  #     - /app/node_modules
  #   environment:
  #     - REACT_APP_API_URL=http://localhost:3000
  #   depends_on:
  #     - backend
  #   networks:
  #     - bookstore-network

networks:
  bookstore-network:
    driver: bridge

volumes:
  mongo-data:
  redis-data:
