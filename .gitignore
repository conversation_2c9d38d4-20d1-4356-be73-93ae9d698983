# Dependencies
node_modules/
/.pnp
.pnp.js

# Build outputs
/frontend/build/
/backend/dist/

# Debug logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# IDE and editor files
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Testing
/coverage
/backend/coverage

# Temporary files
*.log
*.tmp

# System Files
.DS_Store
Thumbs.db

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity