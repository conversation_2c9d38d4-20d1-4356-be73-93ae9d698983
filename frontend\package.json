{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fontsource/roboto": "^5.2.5", "@mui/icons-material": "^7.1.0", "@mui/material": "^7.1.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.126", "@types/react": "^19.1.4", "@types/react-dom": "^19.1.5", "@types/recharts": "^1.8.29", "axios": "^1.9.0", "date-fns": "^4.1.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.0", "react-scripts": "5.0.1", "recharts": "^2.15.3", "typescript": "^4.9.5", "web-vitals": "^2.1.4", "zustand": "^5.0.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}