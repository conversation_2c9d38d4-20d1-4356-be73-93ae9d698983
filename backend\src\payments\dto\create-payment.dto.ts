import {
  IsString,
  IsNotEmpty,
  IsNumber,
  Min,
  IsEnum,
  IsObject,
  ValidateNested,
  IsOptional,
  ValidateIf,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

// DTO cho thông tin VNPay
export class VNPayDetailsDto {
  @ApiProperty({ description: 'URL callback sau khi thanh toán' })
  @IsString()
  @IsOptional()
  returnUrl?: string;
}

// DTO cho thông tin thẻ ngân hàng
export class BankCardDetailsDto {
  @ApiProperty({ description: 'Số thẻ ngân hàng', example: '****************' })
  @IsString()
  @IsNotEmpty()
  cardNumber: string;

  @ApiProperty({ description: 'Tên chủ thẻ', example: 'NGUYEN VAN A' })
  @IsString()
  @IsNotEmpty()
  cardholderName: string;

  @ApiProperty({ description: '<PERSON><PERSON><PERSON> hết hạn (MM/YY)', example: '12/25' })
  @IsString()
  @IsNotEmpty()
  expiryDate: string;

  @ApiProperty({ description: 'Mã CVV', example: '123' })
  @IsString()
  @IsNotEmpty()
  cvv: string;
}

// DTO chung cho việc tạo thanh toán
export class CreatePaymentDto {
  @ApiProperty({ description: 'ID của đơn hàng cần thanh toán' })
  @IsString()
  @IsNotEmpty()
  orderId: string;

  @ApiProperty({
    description: 'Phương thức thanh toán',
    enum: ['COD', 'VNPAY'],
  })
  @IsEnum(['COD', 'VNPAY'])
  @IsNotEmpty()
  paymentMethod: string;

  @ApiProperty({ description: 'Số tiền thanh toán' })
  @IsNumber()
  @Min(1000) // Số tiền tối thiểu là 1000 VND
  amount: number;

  @ApiProperty({ description: 'Chi tiết thanh toán cho VNPAY' })
  @ValidateIf((o) => o.paymentMethod === 'VNPAY')
  @ValidateNested()
  @Type(() => VNPayDetailsDto)
  @IsObject()
  vnpayDetails?: VNPayDetailsDto;

  @ApiProperty({ description: 'Chi tiết thẻ ngân hàng cho BANK_CARD' })
  @ValidateIf((o) => o.paymentMethod === 'BANK_CARD')
  @ValidateNested()
  @Type(() => BankCardDetailsDto)
  @IsObject()
  bankCardDetails?: BankCardDetailsDto;
}
